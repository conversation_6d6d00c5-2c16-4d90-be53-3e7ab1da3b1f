// AURACRONPCGArsenalIsland.cpp
// Implementação da classe AArsenalIsland para o sistema Prismal Flow
// Usando APIs modernas do Unreal Engine 5.6

#include "PCG/AURACRONPCGArsenalIsland.h"
#include "GAS/AURACRONAttributeSet.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "NiagaraSystem.h"
#include "Components/SphereComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "GameFramework/Character.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/StaticMeshActor.h"
#include "Net/UnrealNetwork.h"
#include "AbilitySystemComponent.h"
#include "AbilitySystemInterface.h"
#include "GameplayEffect.h"
#include "GameplayTagContainer.h"
#include "GameplayTagsManager.h"
#include "Engine/AssetManager.h"
#include "Engine/StreamableManager.h"
#include "TimerManager.h"
#include "Engine/EngineTypes.h"
#include "Logging/StructuredLog.h"
#include "GameplayEffectComponents/TargetTagsGameplayEffectComponent.h"

// Categoria de log específica para Arsenal Island usando UE 5.6
DEFINE_LOG_CATEGORY_STATIC(LogArsenalIsland, Log, All);

AArsenalIsland::AArsenalIsland()
{
    // Configuração padrão usando APIs modernas UE 5.6
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.TickInterval = 0.1f; // Otimização: tick a cada 100ms em vez de todo frame

    // Inicializar propriedades com valores robustos
    WeaponBonusDuration = 45.0f; // Alinhado com documentação Arsenal Island
    WeaponBonusIntensity = 1.75f; // +75% conforme especificação
    AbilityBoostMultiplier = 1.5f;
    AbilityBoostIntensity = 1.25f; // +25% conforme especificação
    AbilityBoostDuration = 60.0f;
    SpecialAmmoCount = 100; // Capacidade inicial de munição especial
    bIsNearEnvironmentTransition = false;
    AccumulatedTime = 0.0f;

    // Configurar componentes específicos da Arsenal Island usando APIs modernas UE 5.6

    // Plataforma de armas central (mantendo compatibilidade com header)
    WeaponPlatform = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("WeaponPlatform"));
    if (WeaponPlatform)
    {
        WeaponPlatform->SetupAttachment(RootComponent);
        WeaponPlatform->SetRelativeLocation(FVector(0.0f, 0.0f, 150.0f));
        WeaponPlatform->SetRelativeScale3D(FVector(2.0f, 2.0f, 0.5f));
        WeaponPlatform->SetCollisionProfileName(TEXT("BlockAll"));
        WeaponPlatform->SetCollisionResponseToChannel(ECC_Pawn, ECR_Block);
        WeaponPlatform->SetCollisionResponseToChannel(ECC_Vehicle, ECR_Block);

        // Adicionar à array para compatibilidade com header que declara TArray<UStaticMeshComponent*> WeaponPlatforms
        WeaponPlatforms.Add(WeaponPlatform);
    }

    // Efeito de energia da plataforma usando APIs modernas UE 5.6
    PlatformEnergyEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("PlatformEnergyEffect"));
    if (PlatformEnergyEffect && WeaponPlatform)
    {
        PlatformEnergyEffect->SetupAttachment(WeaponPlatform);
        PlatformEnergyEffect->SetRelativeLocation(FVector(0.0f, 0.0f, 50.0f));
        PlatformEnergyEffect->SetAutoActivate(true);
        PlatformEnergyEffect->SetTickBehavior(ENiagaraTickBehavior::UsePrereqs);
    }

    // Depósitos de munição usando APIs modernas UE 5.6
    for (int32 i = 0; i < 4; ++i)
    {
        FString ComponentName = FString::Printf(TEXT("AmmoDeposit_%d"), i);
        UStaticMeshComponent* AmmoDeposit = CreateDefaultSubobject<UStaticMeshComponent>(*ComponentName);
        if (AmmoDeposit)
        {
            AmmoDeposit->SetupAttachment(RootComponent);

            // Posicionar em torno da plataforma central usando matemática robusta
            const float Angle = (static_cast<float>(i) / 4.0f) * 2.0f * PI;
            const float Distance = 200.0f;
            const FVector Position(
                Distance * FMath::Cos(Angle),
                Distance * FMath::Sin(Angle),
                100.0f
            );

            AmmoDeposit->SetRelativeLocation(Position);
            AmmoDeposit->SetRelativeScale3D(FVector(0.8f, 0.8f, 1.2f));
            AmmoDeposit->SetCollisionProfileName(TEXT("BlockAll"));
            AmmoDeposit->SetCollisionResponseToChannel(ECC_Pawn, ECR_Block);
            AmmoDeposit->SetCollisionResponseToChannel(ECC_Vehicle, ECR_Block);
            AmmoDeposit->SetCollisionResponseToChannel(ECC_GameTraceChannel1, ECR_Block); // ECC_GameTraceChannel1 usado como canal de projétil customizado

            // Adicionar a ambos arrays para compatibilidade total com header
            AmmoDeposits.Add(AmmoDeposit);
            AmmoDepots.Add(AmmoDeposit); // Compatibilidade com nome alternativo no header

            UE_LOGFMT(LogArsenalIsland, Log, "Arsenal Island: Criado depósito de munição {0} na posição {1}", i, Position.ToString());
        }
        else
        {
            UE_LOGFMT(LogArsenalIsland, Error, "Arsenal Island: Falha ao criar depósito de munição {0}", i);
        }
    }

    // Rampas táticas usando APIs modernas UE 5.6
    for (int32 i = 0; i < 2; ++i)
    {
        FString ComponentName = FString::Printf(TEXT("TacticalRamp_%d"), i);
        UStaticMeshComponent* TacticalRamp = CreateDefaultSubobject<UStaticMeshComponent>(*ComponentName);
        if (TacticalRamp)
        {
            TacticalRamp->SetupAttachment(RootComponent);

            // Posicionar em lados opostos usando matemática robusta
            const float Angle = (static_cast<float>(i) / 2.0f) * PI;
            const float Distance = 300.0f;
            const FVector Position(
                Distance * FMath::Cos(Angle),
                Distance * FMath::Sin(Angle),
                50.0f
            );

            TacticalRamp->SetRelativeLocation(Position);
            TacticalRamp->SetRelativeScale3D(FVector(3.0f, 1.0f, 0.5f));
            TacticalRamp->SetRelativeRotation(FRotator(30.0f, Angle * 180.0f / PI, 0.0f));
            TacticalRamp->SetCollisionProfileName(TEXT("BlockAll"));
            TacticalRamp->SetCollisionResponseToChannel(ECC_Pawn, ECR_Block);
            TacticalRamp->SetCollisionResponseToChannel(ECC_Vehicle, ECR_Block);
            TacticalRamp->SetCollisionResponseToChannel(ECC_WorldStatic, ECR_Block);

            TacticalRamps.Add(TacticalRamp);

            UE_LOGFMT(LogArsenalIsland, Log, "Arsenal Island: Criada rampa tática {0} na posição {1} com rotação {2}",
                i, Position.ToString(), FRotator(30.0f, Angle * 180.0f / PI, 0.0f).ToString());
        }
        else
        {
            UE_LOGFMT(LogArsenalIsland, Error, "Arsenal Island: Falha ao criar rampa tática {0}", i);
        }
    }

    // Definir o tipo de ilha como Arsenal conforme documentação
    IslandType = EPrismalFlowIslandType::Arsenal;

    // Configurar replicação moderna UE 5.6
    bReplicates = true;
    SetReplicateMovement(true);
    SetNetUpdateFrequency(10.0f); // Otimização de rede usando API moderna UE 5.6
    SetMinNetUpdateFrequency(2.0f); // API moderna UE 5.6

    UE_LOG(LogArsenalIsland, Log, TEXT("Arsenal Island: Construtor concluído com sucesso - Tipo: Arsenal, Componentes criados: %d depósitos, %d rampas"),
        AmmoDeposits.Num(), TacticalRamps.Num());
}

void AArsenalIsland::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Validação robusta antes de processar
    if (!IsValid(this) || !GetWorld())
    {
        UE_LOGFMT(LogArsenalIsland, Warning, "Arsenal Island: Tick chamado em objeto inválido ou mundo nulo");
        return;
    }

    // Acumular tempo para efeitos visuais
    AccumulatedTime += DeltaTime;

    // Implementar rotação da plataforma de armas usando APIs modernas UE 5.6
    if (IsValid(WeaponPlatform) && bIsActive)
    {
        // Rotação lenta da plataforma com validação robusta
        const FRotator CurrentRotation = WeaponPlatform->GetRelativeRotation();
        const float RotationSpeed = 10.0f; // 10 graus por segundo conforme especificação
        FRotator NewRotation = CurrentRotation;
        NewRotation.Yaw += DeltaTime * RotationSpeed;

        // Normalizar rotação para evitar overflow
        NewRotation.Normalize();
        WeaponPlatform->SetRelativeRotation(NewRotation);

        // Pulsar efeitos de energia usando APIs modernas UE 5.6
        const float Time = GetGameTimeSinceCreation();
        const float PulseValue = 0.5f + 0.5f * FMath::Sin(Time * 2.0f);
        const float IntensityMultiplier = bIsNearEnvironmentTransition ? 1.5f : 1.0f;

        if (IsValid(PlatformEnergyEffect))
        {
            // Usar APIs modernas de Niagara para UE 5.6
            PlatformEnergyEffect->SetFloatParameter(FName("Intensity"), PulseValue * 2.0f * IntensityMultiplier);
            PlatformEnergyEffect->SetFloatParameter(FName("RotationSpeed"), RotationSpeed);
            PlatformEnergyEffect->SetFloatParameter(FName("ActivityLevel"), bIsActive ? 1.0f : 0.3f);

            // Configurar cor baseada no ambiente de transição
            if (TransitionEnvironments.Num() > 0)
            {
                FLinearColor EnvironmentColor = GetEnvironmentTransitionColor(TransitionEnvironments[0]);
                PlatformEnergyEffect->SetColorParameter(FName("EnvironmentColor"), EnvironmentColor);
            }
        }
        else
        {
            UE_LOG(LogArsenalIsland, Warning, TEXT("Arsenal Island: PlatformEnergyEffect é inválido durante Tick"));
        }
    }

    // Atualizar efeitos visuais dos depósitos de munição
    UpdateAmmoDepositEffects(DeltaTime);

    // Processar limpeza de efeitos expirados
    CleanupExpiredEffects();
}

void AArsenalIsland::ApplyIslandEffect(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    // Validações robustas usando APIs modernas UE 5.6
    if (!IsValid(this) || !GetWorld())
    {
        UE_LOGFMT(LogArsenalIsland, Error, "Arsenal Island: ApplyIslandEffect chamado em objeto inválido");
        return;
    }

    // Verificar se a ilha está ativa e componentes são válidos
    if (!bIsActive || !IsValid(OtherActor) || !IsValid(OverlappedComponent))
    {
        UE_LOGFMT(LogArsenalIsland, Verbose, "Arsenal Island: Efeito não aplicado - Ilha inativa ou atores inválidos");
        return;
    }

    // Verificar se o ator é um personagem jogável com validação robusta
    ACharacter* Character = Cast<ACharacter>(OtherActor);
    if (!IsValid(Character))
    {
        UE_LOGFMT(LogArsenalIsland, Verbose, "Arsenal Island: Ator {0} não é um personagem válido", *OtherActor->GetName());
        return;
    }

    // Verificar se já não está sendo processado para evitar spam
    TWeakObjectPtr<AActor> ActorPtr(OtherActor);
    if (AffectedActors.Contains(ActorPtr))
    {
        UE_LOGFMT(LogArsenalIsland, Verbose, "Arsenal Island: Ator {0} já está sendo processado", *OtherActor->GetName());
        return;
    }

    // Adicionar à lista de atores afetados
    AffectedActors.Add(ActorPtr);

    // Aplicar efeito visual de feedback usando APIs modernas UE 5.6
    if (IsValid(PlatformEnergyEffect))
    {
        const float IntensityBoost = 3.0f;
        PlatformEnergyEffect->SetFloatParameter(FName("EffectIntensity"), IntensityBoost);
        PlatformEnergyEffect->SetFloatParameter(FName("PlayerInteraction"), 1.0f);

        // Retornar à intensidade normal após um período usando timer moderno
        FTimerHandle TimerHandle;
        FTimerDelegate TimerDelegate;
        TimerDelegate.BindUFunction(this, FName("ResetPlatformEffectIntensity"));

        GetWorldTimerManager().SetTimer(TimerHandle, TimerDelegate, 0.5f, false);

        UE_LOGFMT(LogArsenalIsland, Log, "Arsenal Island: Efeito visual intensificado para interação com {0}", *Character->GetName());
    }

    // Conceder todos os bônus do Arsenal Island conforme documentação
    GrantWeaponBonus(OtherActor);
    GrantSpecialAmmo(OtherActor);
    GrantAbilityBoost(OtherActor);

    UE_LOGFMT(LogArsenalIsland, Log, "Arsenal Island: Efeitos completos aplicados ao personagem {0}", *Character->GetName());
}

void AArsenalIsland::GrantWeaponBonus(AActor* TargetActor)
{
    // Validações robustas usando APIs modernas UE 5.6
    if (!IsValid(TargetActor) || !IsValid(this) || !GetWorld())
    {
        UE_LOGFMT(LogArsenalIsland, Error, "Arsenal Island: GrantWeaponBonus chamado com parâmetros inválidos");
        return;
    }

    // Verificar se o ator implementa a interface do sistema de habilidades com validação robusta
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        UE_LOGFMT(LogArsenalIsland, Warning, "Arsenal Island: Ator {0} não implementa AbilitySystemInterface para bônus de armas", *TargetActor->GetName());
        return;
    }

    // Obter o componente do sistema de habilidades com validação robusta
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!IsValid(AbilityComponent))
    {
        UE_LOGFMT(LogArsenalIsland, Warning, "Arsenal Island: Ator {0} não possui AbilitySystemComponent válido para bônus de armas", *TargetActor->GetName());
        return;
    }

    // Usar GameplayEffect pré-configurado se disponível, senão criar dinamicamente
    TSubclassOf<UGameplayEffect> EffectClass = WeaponBonusEffect;
    UGameplayEffect* LocalWeaponBonusEffect = nullptr;

    if (!EffectClass)
    {
        // Criar GameplayEffect específico usando APIs modernas UE 5.6
        LocalWeaponBonusEffect = NewObject<UGameplayEffect>(this, UGameplayEffect::StaticClass(), FName(TEXT("GE_ArsenalIslandWeaponBonus")));
        if (IsValid(LocalWeaponBonusEffect))
        {
            // Configurar duração do efeito conforme documentação Arsenal Island
            LocalWeaponBonusEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
            LocalWeaponBonusEffect->DurationMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(WeaponBonusDuration));

            // Configurar tags para identificação e remoção usando APIs modernas UE 5.6
            FGameplayTag ArsenalWeaponTag = FGameplayTag::RequestGameplayTag(FName("Effect.Arsenal.WeaponBonus"));

            // Usar UTargetTagsGameplayEffectComponent para configurar tags (API moderna UE 5.6)
            UTargetTagsGameplayEffectComponent* TargetTagsComponent = NewObject<UTargetTagsGameplayEffectComponent>(LocalWeaponBonusEffect);
            if (TargetTagsComponent)
            {
                TargetTagsComponent->SetAndApplyTargetTagChanges(FInheritedTagContainer(), FInheritedTagContainer(), FInheritedTagContainer(), FInheritedTagContainer());
                LocalWeaponBonusEffect->AddComponent(TargetTagsComponent);
            }

            // Configurar modificadores específicos do Arsenal Island conforme documentação
            FGameplayModifierInfo WeaponDamageModifier;
            WeaponDamageModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
            WeaponDamageModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(WeaponBonusIntensity)); // +75% dano
            WeaponDamageModifier.Attribute = UAURACRONAttributeSet::GetAttackDamageAttribute(); // Usar atributo existente

            FGameplayModifierInfo WeaponSpeedModifier;
            WeaponSpeedModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
            WeaponSpeedModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(1.3f)); // +30% velocidade de ataque
            WeaponSpeedModifier.Attribute = UAURACRONAttributeSet::GetAttackSpeedAttribute(); // Usar atributo existente

            FGameplayModifierInfo WeaponSpeedModifier;
            WeaponSpeedModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
            WeaponSpeedModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(1.3f)); // +30% velocidade de ataque
            WeaponSpeedModifier.Attribute = UAURACRONAttributeSet::GetAttackSpeedAttribute();

            // Adicionar todos os modificadores ao efeito
            LocalWeaponBonusEffect->Modifiers.Add(WeaponDamageModifier);
            LocalWeaponBonusEffect->Modifiers.Add(WeaponRangeModifier);
            LocalWeaponBonusEffect->Modifiers.Add(WeaponSpeedModifier);

            EffectClass = LocalWeaponBonusEffect->GetClass();
        }
        else
        {
            UE_LOGFMT(LogArsenalIsland, Error, "Arsenal Island: Falha ao criar GameplayEffect para bônus de armas");
            return;
        }
    }

    // Aplicar efeito de bônus de armas usando APIs modernas UE 5.6
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddInstigator(this, TargetActor);

    FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(EffectClass, 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());

        if (ActiveEffect.IsValid())
        {
            // Armazenar efeito ativo para rastreamento
            TWeakObjectPtr<AActor> ActorPtr(TargetActor);
            if (!ActiveArsenalEffects.Contains(ActorPtr))
            {
                ActiveArsenalEffects.Add(ActorPtr, TArray<FActiveGameplayEffectHandle>());
            }
            ActiveArsenalEffects[ActorPtr].Add(ActiveEffect);

            UE_LOG(LogArsenalIsland, Log, TEXT("Arsenal Island: Bônus de armas aplicado com sucesso para %s (+%.1f%% dano, +50%% alcance, +30%% velocidade por %.1fs)"),
                *TargetActor->GetName(), (WeaponBonusIntensity - 1.0f) * 100.0f, WeaponBonusDuration);
        }
        else
        {
            UE_LOG(LogArsenalIsland, Error, TEXT("Arsenal Island: Falha ao aplicar efeito de bônus de armas para %s"), *TargetActor->GetName());
        }
    }
    else
    {
        UE_LOGFMT(LogArsenalIsland, Error, "Arsenal Island: Falha ao criar spec de efeito de bônus de armas para {0}", *TargetActor->GetName());
    }

    // Criar feedback visual de bônus de armas usando carregamento assíncrono UE 5.6
    LoadAndSpawnWeaponBonusVFX(TargetActor);
}

void AArsenalIsland::GrantSpecialAmmo(AActor* TargetActor)
{
    // Validações robustas usando APIs modernas UE 5.6
    if (!IsValid(TargetActor) || !IsValid(this) || !GetWorld())
    {
        UE_LOGFMT(LogArsenalIsland, Error, "Arsenal Island: GrantSpecialAmmo chamado com parâmetros inválidos");
        return;
    }

    // Verificar se há munição especial disponível
    if (SpecialAmmoCount <= 0)
    {
        UE_LOGFMT(LogArsenalIsland, Warning, "Arsenal Island: Sem munição especial disponível para {0}", *TargetActor->GetName());
        return;
    }

    // Verificar se o ator implementa a interface do sistema de habilidades com validação robusta
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        UE_LOGFMT(LogArsenalIsland, Warning, "Arsenal Island: Ator {0} não implementa AbilitySystemInterface para munição especial", *TargetActor->GetName());
        return;
    }

    // Obter o componente do sistema de habilidades com validação robusta
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!IsValid(AbilityComponent))
    {
        UE_LOGFMT(LogArsenalIsland, Warning, "Arsenal Island: Ator {0} não possui AbilitySystemComponent válido para munição especial", *TargetActor->GetName());
        return;
    }

    // Usar GameplayEffect pré-configurado se disponível, senão criar dinamicamente
    TSubclassOf<UGameplayEffect> EffectClass = SpecialAmmoEffect;
    UGameplayEffect* LocalSpecialAmmoEffect = nullptr;

    if (!EffectClass)
    {
        // Criar GameplayEffect específico usando APIs modernas UE 5.6
        LocalSpecialAmmoEffect = NewObject<UGameplayEffect>(this, UGameplayEffect::StaticClass(), FName(TEXT("GE_ArsenalIslandSpecialAmmo")));
        if (IsValid(LocalSpecialAmmoEffect))
        {
            // Configurar duração do efeito conforme documentação Arsenal Island
            LocalSpecialAmmoEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
            LocalSpecialAmmoEffect->DurationMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(60.0f));

            // Configurar tags para identificação e remoção usando APIs modernas UE 5.6
            FGameplayTag ArsenalAmmoTag = FGameplayTag::RequestGameplayTag(FName("Effect.Arsenal.SpecialAmmo"));

            // Usar UTargetTagsGameplayEffectComponent para configurar tags (API moderna UE 5.6)
            UTargetTagsGameplayEffectComponent* AmmoTargetTagsComponent = NewObject<UTargetTagsGameplayEffectComponent>(LocalSpecialAmmoEffect);
            if (AmmoTargetTagsComponent)
            {
                AmmoTargetTagsComponent->SetAndApplyTargetTagChanges(FInheritedTagContainer(), FInheritedTagContainer(), FInheritedTagContainer(), FInheritedTagContainer());
                LocalSpecialAmmoEffect->AddComponent(AmmoTargetTagsComponent);
            }

            // Configurar modificadores específicos para munição especial conforme documentação
            FGameplayModifierInfo AmmoCapacityModifier;
            AmmoCapacityModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
            AmmoCapacityModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(2.0f)); // +100% capacidade
            AmmoCapacityModifier.Attribute = UAURACRONAttributeSet::GetAttackDamageAttribute(); // Usar atributo existente como proxy

            FGameplayModifierInfo AmmoPenetrationModifier;
            AmmoPenetrationModifier.ModifierOp = EGameplayModOp::Additive;
            AmmoPenetrationModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(50.0f)); // +50 penetração
            AmmoPenetrationModifier.Attribute = UAURACRONAttributeSet::GetAccuracyAttribute(); // Usar atributo existente como proxy

            FGameplayModifierInfo AmmoExplosiveModifier;
            AmmoExplosiveModifier.ModifierOp = EGameplayModOp::Additive;
            AmmoExplosiveModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(25.0f)); // +25 dano explosivo
            AmmoExplosiveModifier.Attribute = UAURACRONAttributeSet::GetAbilityPowerAttribute(); // Usar atributo existente como proxy

            // Adicionar modificadores ao efeito
            LocalSpecialAmmoEffect->Modifiers.Add(AmmoCapacityModifier);
            LocalSpecialAmmoEffect->Modifiers.Add(AmmoPenetrationModifier);
            LocalSpecialAmmoEffect->Modifiers.Add(AmmoExplosiveModifier);

            EffectClass = LocalSpecialAmmoEffect->GetClass();
        }
        else
        {
            UE_LOGFMT(LogArsenalIsland, Error, "Arsenal Island: Falha ao criar GameplayEffect para munição especial");
            return;
        }
    }

    // Aplicar efeito de munição especial usando APIs modernas UE 5.6
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddInstigator(this, TargetActor);

    FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(EffectClass, 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());

        if (ActiveEffect.IsValid())
        {
            // Consumir munição especial
            SpecialAmmoCount = FMath::Max(0, SpecialAmmoCount - 1);

            // Armazenar efeito ativo para rastreamento
            TWeakObjectPtr<AActor> ActorPtr(TargetActor);
            if (!ActiveArsenalEffects.Contains(ActorPtr))
            {
                ActiveArsenalEffects.Add(ActorPtr, TArray<FActiveGameplayEffectHandle>());
            }
            ActiveArsenalEffects[ActorPtr].Add(ActiveEffect);

            UE_LOGFMT(LogArsenalIsland, Log, "Arsenal Island: Munição especial aplicada com sucesso para {0} (+100% capacidade, +50 penetração, +25 explosivo). Restante: {1}",
                *TargetActor->GetName(), SpecialAmmoCount);

            // Replicar mudança na contagem de munição
            if (HasAuthority())
            {
                ForceNetUpdate();
            }
        }
        else
        {
            UE_LOGFMT(LogArsenalIsland, Error, "Arsenal Island: Falha ao aplicar efeito de munição especial para {0}", *TargetActor->GetName());
        }
    }
    else
    {
        UE_LOGFMT(LogArsenalIsland, Error, "Arsenal Island: Falha ao criar spec de efeito de munição especial para {0}", *TargetActor->GetName());
    }

    // Criar feedback visual de munição especial usando carregamento assíncrono UE 5.6
    LoadAndSpawnSpecialAmmoVFX(TargetActor);
}

void AArsenalIsland::GrantAbilityBoost(AActor* TargetActor)
{
    // Validações robustas usando APIs modernas UE 5.6
    if (!IsValid(TargetActor) || !IsValid(this) || !GetWorld())
    {
        UE_LOGFMT(LogArsenalIsland, Error, "Arsenal Island: GrantAbilityBoost chamado com parâmetros inválidos");
        return;
    }

    // Verificar se o ator implementa a interface do sistema de habilidades com validação robusta
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        UE_LOGFMT(LogArsenalIsland, Warning, "Arsenal Island: Ator {0} não implementa AbilitySystemInterface para potencialização de habilidades", *TargetActor->GetName());
        return;
    }

    // Obter o componente do sistema de habilidades com validação robusta
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!IsValid(AbilityComponent))
    {
        UE_LOGFMT(LogArsenalIsland, Warning, "Arsenal Island: Ator {0} não possui AbilitySystemComponent válido para potencialização de habilidades", *TargetActor->GetName());
        return;
    }

    // Usar GameplayEffect pré-configurado se disponível, senão criar dinamicamente
    TSubclassOf<UGameplayEffect> EffectClass = AbilityBoostEffect;
    UGameplayEffect* LocalAbilityBoostEffect = nullptr;

    if (!EffectClass)
    {
        // Criar GameplayEffect específico usando APIs modernas UE 5.6
        LocalAbilityBoostEffect = NewObject<UGameplayEffect>(this, UGameplayEffect::StaticClass(), FName("GE_ArsenalIslandAbilityBoost"));
        if (IsValid(LocalAbilityBoostEffect))
        {
            // Configurar duração do efeito conforme documentação Arsenal Island
            LocalAbilityBoostEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
            LocalAbilityBoostEffect->DurationMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(AbilityBoostDuration));

            // Configurar tags para identificação e remoção usando APIs modernas UE 5.6
            FGameplayTag ArsenalAbilityTag = FGameplayTag::RequestGameplayTag(FName("Effect.Arsenal.AbilityBoost"));

            // Usar UTargetTagsGameplayEffectComponent para configurar tags (API moderna UE 5.6)
            UTargetTagsGameplayEffectComponent* AbilityTargetTagsComponent = NewObject<UTargetTagsGameplayEffectComponent>(LocalAbilityBoostEffect);
            if (AbilityTargetTagsComponent)
            {
                AbilityTargetTagsComponent->SetAndApplyTargetTagChanges(FInheritedTagContainer(), FInheritedTagContainer(), FInheritedTagContainer(), FInheritedTagContainer());
                LocalAbilityBoostEffect->AddComponent(AbilityTargetTagsComponent);
            }

            // Modificador para redução de cooldown (-50%) conforme documentação
            FGameplayModifierInfo CooldownModifier;
            CooldownModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(0.5f));
            CooldownModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
            CooldownModifier.Attribute = UAURACRONAttributeSet::GetCooldownReductionAttribute();

            // Modificador para aumento de poder de habilidades (+25%) conforme documentação
            FGameplayModifierInfo PowerModifier;
            PowerModifier.ModifierMagnitude = FGameplayEffectModifierMagnitude(FScalableFloat(AbilityBoostIntensity));
            PowerModifier.ModifierOp = EGameplayModOp::MultiplyAdditive;
            PowerModifier.Attribute = UAURACRONAttributeSet::GetAbilityPowerAttribute();

            // Adicionar modificadores ao efeito
            LocalAbilityBoostEffect->Modifiers.Add(CooldownModifier);
            LocalAbilityBoostEffect->Modifiers.Add(PowerModifier);

            EffectClass = LocalAbilityBoostEffect->GetClass();
        }
        else
        {
            UE_LOGFMT(LogArsenalIsland, Error, "Arsenal Island: Falha ao criar GameplayEffect para potencialização de habilidades");
            return;
        }
    }

    // Aplicar efeito de potencialização de habilidades usando APIs modernas UE 5.6
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    EffectContext.AddInstigator(this, TargetActor);

    FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(EffectClass, 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());

        if (ActiveEffect.IsValid())
        {
            // Armazenar efeito ativo para rastreamento
            TWeakObjectPtr<AActor> ActorPtr(TargetActor);
            if (!ActiveArsenalEffects.Contains(ActorPtr))
            {
                ActiveArsenalEffects.Add(ActorPtr, TArray<FActiveGameplayEffectHandle>());
            }
            ActiveArsenalEffects[ActorPtr].Add(ActiveEffect);

            UE_LOG(LogArsenalIsland, Log, TEXT("Arsenal Island: Potencialização de habilidades aplicada com sucesso para %s (Cooldown -50%%, Poder +%.1f%% por %.1fs)"),
                *TargetActor->GetName(), (AbilityBoostIntensity - 1.0f) * 100.0f, AbilityBoostDuration);
        }
        else
        {
            UE_LOG(LogArsenalIsland, Error, TEXT("Arsenal Island: Falha ao aplicar efeito de potencialização de habilidades para %s"), *TargetActor->GetName());
        }
    }
    else
    {
        UE_LOG(LogArsenalIsland, Error, TEXT("Arsenal Island: Falha ao criar spec de efeito de potencialização de habilidades para %s"), *TargetActor->GetName());
    }

    // Criar feedback visual de potencialização usando carregamento assíncrono UE 5.6
    LoadAndSpawnAbilityBoostVFX(TargetActor);
}

// Implementação das funções auxiliares usando APIs modernas do UE 5.6

FLinearColor AArsenalIsland::GetEnvironmentTransitionColor(EAURACRONEnvironmentType EnvironmentType) const
{
    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            return FLinearColor::Yellow;
        case EAURACRONEnvironmentType::ZephyrFirmament:
            return FLinearColor(0.0f, 1.0f, 1.0f); // Cyan
        case EAURACRONEnvironmentType::PurgatoryRealm:
            return FLinearColor(0.8f, 0.2f, 0.8f); // Violeta espectral
        default:
            return FLinearColor::Blue;
    }
}

void AArsenalIsland::UpdateAmmoDepositEffects(float DeltaTime)
{
    if (!IsValid(this) || AmmoDeposits.Num() == 0)
    {
        return;
    }

    // Atualizar efeitos visuais dos depósitos baseado na quantidade de munição
    const float AmmoRatio = static_cast<float>(SpecialAmmoCount) / 100.0f; // Assumindo 100 como máximo
    const float PulseSpeed = FMath::Lerp(0.5f, 2.0f, AmmoRatio);
    const float GlowIntensity = FMath::Lerp(0.3f, 1.0f, AmmoRatio);

    for (int32 i = 0; i < AmmoDeposits.Num(); ++i)
    {
        if (IsValid(AmmoDeposits[i]))
        {
            // Atualizar material dinâmico se existir
            if (UMaterialInterface* BaseMaterial = AmmoDeposits[i]->GetMaterial(0))
            {
                UMaterialInstanceDynamic* DynamicMaterial = AmmoDeposits[i]->CreateDynamicMaterialInstance(0, BaseMaterial);
                if (DynamicMaterial)
                {
                    const float TimeOffset = static_cast<float>(i) * 0.5f; // Offset para cada depósito
                    const float PulseValue = 0.5f + 0.5f * FMath::Sin((AccumulatedTime + TimeOffset) * PulseSpeed);

                    DynamicMaterial->SetScalarParameterValue(FName("AmmoLevel"), AmmoRatio);
                    DynamicMaterial->SetScalarParameterValue(FName("GlowIntensity"), GlowIntensity * PulseValue);
                    DynamicMaterial->SetVectorParameterValue(FName("AmmoColor"), FVector(1.0f, 0.5f, 0.0f)); // Laranja
                }
            }
        }
    }
}

void AArsenalIsland::CleanupExpiredEffects()
{
    if (!IsValid(this) || !HasAuthority())
    {
        return;
    }

    // Limpar efeitos de atores que não são mais válidos
    TArray<TWeakObjectPtr<AActor>> ActorsToRemove;

    for (auto& EffectPair : ActiveArsenalEffects)
    {
        if (!EffectPair.Key.IsValid())
        {
            ActorsToRemove.Add(EffectPair.Key);
        }
    }

    for (const TWeakObjectPtr<AActor>& ActorPtr : ActorsToRemove)
    {
        ActiveArsenalEffects.Remove(ActorPtr);
        AffectedActors.Remove(ActorPtr);
    }

    // Limpar lista de atores afetados
    AffectedActors.RemoveAll([](const TWeakObjectPtr<AActor>& ActorPtr)
    {
        return !ActorPtr.IsValid();
    });
}

void AArsenalIsland::ResetPlatformEffectIntensity()
{
    if (IsValid(PlatformEnergyEffect))
    {
        PlatformEnergyEffect->SetFloatParameter(FName("EffectIntensity"), 1.0f);
        PlatformEnergyEffect->SetFloatParameter(FName("PlayerInteraction"), 0.0f);
    }
}

void AArsenalIsland::LoadAndSpawnWeaponBonusVFX(AActor* TargetActor)
{
    if (!IsValid(TargetActor) || !GetWorld())
    {
        return;
    }

    // Usar carregamento assíncrono moderno UE 5.6
    FStreamableManager& StreamableManager = UAssetManager::GetStreamableManager();
    FSoftObjectPath VFXPath(TEXT("/Game/VFX/Islands/NS_ArsenalIslandWeaponBonus"));

    FVector SpawnLocation = TargetActor->GetActorLocation();
    TSharedPtr<FStreamableHandle> Handle = StreamableManager.RequestAsyncLoad(
        VFXPath,
        FStreamableDelegate::CreateLambda([this, SpawnLocation]()
        {
            OnWeaponBonusVFXLoaded(SpawnLocation);
        })
    );

    if (Handle.IsValid())
    {
        UE_LOG(LogArsenalIsland, Verbose, TEXT("Arsenal Island: Carregando VFX de bônus de armas assincronamente"));
    }
}

void AArsenalIsland::LoadAndSpawnSpecialAmmoVFX(AActor* TargetActor)
{
    if (!IsValid(TargetActor) || !GetWorld())
    {
        return;
    }

    // Usar carregamento assíncrono moderno UE 5.6
    FStreamableManager& StreamableManager = UAssetManager::GetStreamableManager();
    FSoftObjectPath VFXPath(TEXT("/Game/VFX/Islands/NS_ArsenalIslandSpecialAmmo"));

    FVector SpawnLocation = TargetActor->GetActorLocation();
    TSharedPtr<FStreamableHandle> Handle = StreamableManager.RequestAsyncLoad(
        VFXPath,
        FStreamableDelegate::CreateLambda([this, SpawnLocation]()
        {
            OnSpecialAmmoVFXLoaded(SpawnLocation);
        })
    );

    if (Handle.IsValid())
    {
        UE_LOG(LogArsenalIsland, Verbose, TEXT("Arsenal Island: Carregando VFX de munição especial assincronamente"));
    }
}

void AArsenalIsland::LoadAndSpawnAbilityBoostVFX(AActor* TargetActor)
{
    if (!IsValid(TargetActor) || !GetWorld())
    {
        return;
    }

    // Usar carregamento assíncrono moderno UE 5.6
    FStreamableManager& StreamableManager = UAssetManager::GetStreamableManager();
    FSoftObjectPath VFXPath(TEXT("/Game/VFX/Islands/NS_ArsenalIslandAbilityBoost"));

    FVector SpawnLocation = TargetActor->GetActorLocation();
    TSharedPtr<FStreamableHandle> Handle = StreamableManager.RequestAsyncLoad(
        VFXPath,
        FStreamableDelegate::CreateLambda([this, SpawnLocation]()
        {
            OnAbilityBoostVFXLoaded(SpawnLocation);
        })
    );

    if (Handle.IsValid())
    {
        UE_LOG(LogArsenalIsland, Verbose, TEXT("Arsenal Island: Carregando VFX de potencialização de habilidades assincronamente"));
    }
}

void AArsenalIsland::OnWeaponBonusVFXLoaded(FVector SpawnLocation)
{
    UNiagaraSystem* WeaponBonusVFX = Cast<UNiagaraSystem>(UAssetManager::GetStreamableManager().LoadSynchronous(FSoftObjectPath(TEXT("/Game/VFX/Islands/NS_ArsenalIslandWeaponBonus"))));
    if (WeaponBonusVFX && GetWorld())
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            WeaponBonusVFX,
            SpawnLocation,
            FRotator::ZeroRotator,
            FVector(1.0f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
        UE_LOG(LogArsenalIsland, Log, TEXT("Arsenal Island: VFX de bônus de armas spawned na localização %s"), *SpawnLocation.ToString());
    }
}

void AArsenalIsland::OnSpecialAmmoVFXLoaded(FVector SpawnLocation)
{
    UNiagaraSystem* SpecialAmmoVFX = Cast<UNiagaraSystem>(UAssetManager::GetStreamableManager().LoadSynchronous(FSoftObjectPath(TEXT("/Game/VFX/Islands/NS_ArsenalIslandSpecialAmmo"))));
    if (SpecialAmmoVFX && GetWorld())
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            SpecialAmmoVFX,
            SpawnLocation,
            FRotator::ZeroRotator,
            FVector(0.8f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
        UE_LOG(LogArsenalIsland, Log, TEXT("Arsenal Island: VFX de munição especial spawned na localização %s"), *SpawnLocation.ToString());
    }
}

void AArsenalIsland::OnAbilityBoostVFXLoaded(FVector SpawnLocation)
{
    UNiagaraSystem* AbilityBoostVFX = Cast<UNiagaraSystem>(UAssetManager::GetStreamableManager().LoadSynchronous(FSoftObjectPath(TEXT("/Game/VFX/Islands/NS_ArsenalIslandAbilityBoost"))));
    if (AbilityBoostVFX && GetWorld())
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            AbilityBoostVFX,
            SpawnLocation,
            FRotator::ZeroRotator,
            FVector(1.1f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
        UE_LOG(LogArsenalIsland, Log, TEXT("Arsenal Island: VFX de potencialização de habilidades spawned na localização %s"), *SpawnLocation.ToString());
    }
}

// Implementação das funções ausentes usando APIs modernas do UE 5.6

bool AArsenalIsland::IsNearEnvironmentTransition() const
{
    return bIsNearEnvironmentTransition;
}

void AArsenalIsland::SetNearEnvironmentTransition(bool bIsNearTransition)
{
    bIsNearEnvironmentTransition = bIsNearTransition;

    // Atualizar efeitos visuais baseado na proximidade de transição
    if (PlatformEnergyEffect)
    {
        if (bIsNearEnvironmentTransition)
        {
            // Intensificar efeitos quando próximo a transições
            PlatformEnergyEffect->SetFloatParameter(FName("IntensityMultiplier"), 1.5f);
            PlatformEnergyEffect->SetVectorParameter(FName("TransitionGlow"), FVector(1.0f, 0.8f, 0.2f));
        }
        else
        {
            // Efeitos normais
            PlatformEnergyEffect->SetFloatParameter(FName("IntensityMultiplier"), 1.0f);
            PlatformEnergyEffect->SetVectorParameter(FName("TransitionGlow"), FVector(0.5f, 0.5f, 1.0f));
        }
    }

    // Replicar mudança para clientes
    if (HasAuthority())
    {
        ForceNetUpdate();
    }
}

void AArsenalIsland::RemoveArsenalEffects(AActor* TargetActor)
{
    if (!TargetActor)
    {
        return;
    }

    // Buscar componente de sistema de habilidades usando APIs modernas do UE 5.6
    if (IAbilitySystemInterface* ASI = Cast<IAbilitySystemInterface>(TargetActor))
    {
        if (UAbilitySystemComponent* ASC = ASI->GetAbilitySystemComponent())
        {
            // Remover todos os efeitos de gameplay aplicados por esta ilha
            FGameplayTagContainer TagsToRemove;
            TagsToRemove.AddTag(FGameplayTag::RequestGameplayTag(FName("Effect.Arsenal.WeaponBonus")));
            TagsToRemove.AddTag(FGameplayTag::RequestGameplayTag(FName("Effect.Arsenal.AbilityBoost")));
            TagsToRemove.AddTag(FGameplayTag::RequestGameplayTag(FName("Effect.Arsenal.SpecialAmmo")));

            // Usar API moderna do UE 5.6 para remoção de efeitos
            ASC->RemoveActiveEffectsWithTags(TagsToRemove);

            UE_LOG(LogTemp, Log, TEXT("AArsenalIsland: Removidos efeitos de arsenal do ator %s"), *TargetActor->GetName());
        }
    }

    // Remover da lista de atores afetados
    TWeakObjectPtr<AActor> ActorPtr(TargetActor);
    ActiveArsenalEffects.Remove(ActorPtr);
    AffectedActors.Remove(ActorPtr);
}

void AArsenalIsland::UpdateIslandVisuals()
{
    // Implementação robusta da atualização visual usando APIs modernas do UE 5.6
    Super::UpdateIslandVisuals();

    if (!IsValid(this) || !GetWorld())
    {
        UE_LOGFMT(LogArsenalIsland, Warning, "Arsenal Island: UpdateIslandVisuals chamado em objeto inválido");
        return;
    }

    // Atualizar efeitos baseado no estado da ilha usando APIs modernas UE 5.6
    FLinearColor PlatformColor = FLinearColor::Blue;

    if (IsValid(PlatformEnergyEffect))
    {
        // Configurar cor baseada no tipo de ambiente usando função auxiliar
        if (TransitionEnvironments.Num() > 0)
        {
            PlatformColor = GetEnvironmentTransitionColor(TransitionEnvironments[0]);
        }

        // Aplicar cor usando APIs modernas UE 5.6
        PlatformEnergyEffect->SetColorParameter(FName("PlatformColor"), PlatformColor);

        // Configurar intensidade baseada na atividade com validação robusta
        const float CurrentActivityLevel = bIsActive ? 1.0f : 0.5f;
        PlatformEnergyEffect->SetFloatParameter(FName("ActivityIntensity"), CurrentActivityLevel);

        // Configurar pulsação baseada na proximidade de transição
        if (bIsNearEnvironmentTransition)
        {
            PlatformEnergyEffect->SetFloatParameter(FName("PulseSpeed"), 2.0f);
            PlatformEnergyEffect->SetFloatParameter(FName("PulseAmplitude"), 0.8f);
            PlatformEnergyEffect->SetFloatParameter(FName("TransitionProximity"), 1.0f);
        }
        else
        {
            PlatformEnergyEffect->SetFloatParameter(FName("PulseSpeed"), 1.0f);
            PlatformEnergyEffect->SetFloatParameter(FName("PulseAmplitude"), 0.5f);
            PlatformEnergyEffect->SetFloatParameter(FName("TransitionProximity"), 0.0f);
        }

        UE_LOGFMT(LogArsenalIsland, Verbose, "Arsenal Island: Efeitos de plataforma atualizados - Cor: {0}, Atividade: {1}",
            PlatformColor.ToString(), CurrentActivityLevel);
    }

    // Atualizar materiais dinâmicos das plataformas de armas (compatibilidade com ambos WeaponPlatform e WeaponPlatforms)
    TArray<UStaticMeshComponent*> AllWeaponPlatforms;

    // Adicionar plataforma singular se válida
    if (IsValid(WeaponPlatform))
    {
        AllWeaponPlatforms.Add(WeaponPlatform);
    }

    // Adicionar plataformas do array se válidas
    for (UStaticMeshComponent* Platform : WeaponPlatforms)
    {
        if (IsValid(Platform) && !AllWeaponPlatforms.Contains(Platform))
        {
            AllWeaponPlatforms.Add(Platform);
        }
    }

    // Processar todas as plataformas
    for (UStaticMeshComponent* Platform : AllWeaponPlatforms)
    {
        if (IsValid(Platform))
        {
            if (UMaterialInterface* BaseMaterial = Platform->GetMaterial(0))
            {
                UMaterialInstanceDynamic* DynamicMaterial = Platform->CreateDynamicMaterialInstance(0, BaseMaterial);
                if (DynamicMaterial)
                {
                    // Configurar parâmetros do material usando APIs modernas UE 5.6
                    DynamicMaterial->SetScalarParameterValue(FName("GlowIntensity"), WeaponBonusIntensity);
                    DynamicMaterial->SetVectorParameterValue(FName("GlowColor"), FVector(PlatformColor.R, PlatformColor.G, PlatformColor.B));
                    DynamicMaterial->SetScalarParameterValue(FName("Metallic"), 0.8f);
                    DynamicMaterial->SetScalarParameterValue(FName("Roughness"), 0.2f);
                    DynamicMaterial->SetScalarParameterValue(FName("EmissiveIntensity"), bIsActive ? 2.0f : 0.5f);

                    UE_LOGFMT(LogArsenalIsland, Verbose, "Arsenal Island: Material de plataforma atualizado com intensidade {0}", WeaponBonusIntensity);
                }
            }
        }
    }

    // Atualizar depósitos de munição (compatibilidade com ambos AmmoDeposits e AmmoDepots)
    TArray<UStaticMeshComponent*> AllAmmoDepots;

    // Adicionar de AmmoDeposits
    for (UStaticMeshComponent* Depot : AmmoDeposits)
    {
        if (IsValid(Depot))
        {
            AllAmmoDepots.Add(Depot);
        }
    }

    // Adicionar de AmmoDepots se não já incluído
    for (UStaticMeshComponent* Depot : AmmoDepots)
    {
        if (IsValid(Depot) && !AllAmmoDepots.Contains(Depot))
        {
            AllAmmoDepots.Add(Depot);
        }
    }

    // Processar todos os depósitos
    for (UStaticMeshComponent* Depot : AllAmmoDepots)
    {
        if (IsValid(Depot))
        {
            // Configurar escala baseada na quantidade de munição disponível com validação robusta
            const float AmmoRatio = FMath::Clamp(static_cast<float>(SpecialAmmoCount) / 100.0f, 0.0f, 1.0f);
            const float ScaleMultiplier = FMath::Lerp(0.5f, 2.0f, AmmoRatio);
            Depot->SetRelativeScale3D(FVector(ScaleMultiplier));

            // Atualizar material usando APIs modernas UE 5.6
            if (UMaterialInterface* BaseMaterial = Depot->GetMaterial(0))
            {
                UMaterialInstanceDynamic* DynamicMaterial = Depot->CreateDynamicMaterialInstance(0, BaseMaterial);
                if (DynamicMaterial)
                {
                    DynamicMaterial->SetScalarParameterValue(FName("AmmoLevel"), AmmoRatio);
                    DynamicMaterial->SetVectorParameterValue(FName("AmmoColor"), FVector(1.0f, 0.5f, 0.0f)); // Laranja para munição
                    DynamicMaterial->SetScalarParameterValue(FName("AmmoGlow"), AmmoRatio * 2.0f);
                    DynamicMaterial->SetScalarParameterValue(FName("ActivityLevel"), bIsActive ? 1.0f : 0.3f);
                }
            }

            UE_LOGFMT(LogArsenalIsland, Verbose, "Arsenal Island: Depósito de munição atualizado - Escala: {0}, Nível: {1}",
                ScaleMultiplier, AmmoRatio);
        }
    }

    UE_LOGFMT(LogArsenalIsland, Verbose, "Arsenal Island: Visuais atualizados - {0} plataformas, {1} depósitos processados",
        AllWeaponPlatforms.Num(), AllAmmoDepots.Num());
}

void AArsenalIsland::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades específicas da Arsenal Island usando APIs modernas do UE 5.6
    // Propriedades críticas replicadas para todos os clientes
    DOREPLIFETIME(AArsenalIsland, WeaponBonusIntensity);
    DOREPLIFETIME(AArsenalIsland, WeaponBonusDuration);
    DOREPLIFETIME(AArsenalIsland, SpecialAmmoCount);
    DOREPLIFETIME(AArsenalIsland, AbilityBoostIntensity);
    DOREPLIFETIME(AArsenalIsland, AbilityBoostDuration);
    DOREPLIFETIME(AArsenalIsland, bIsNearEnvironmentTransition);

    // Propriedades de ambiente replicadas com condição para otimização
    DOREPLIFETIME_CONDITION(AArsenalIsland, TransitionEnvironments, COND_InitialOnly);

    // Replicar GameplayEffects classes se configurados
    DOREPLIFETIME_CONDITION(AArsenalIsland, WeaponBonusEffect, COND_InitialOnly);
    DOREPLIFETIME_CONDITION(AArsenalIsland, SpecialAmmoEffect, COND_InitialOnly);
    DOREPLIFETIME_CONDITION(AArsenalIsland, AbilityBoostEffect, COND_InitialOnly);

    UE_LOGFMT(LogArsenalIsland, Verbose, "Arsenal Island: Propriedades de replicação configuradas usando APIs modernas UE 5.6");
}